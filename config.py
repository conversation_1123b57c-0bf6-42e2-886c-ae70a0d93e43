"""
Configuration management for Telegram Message Scraper
"""
import os
from dotenv import load_dotenv
from typing import List, Optional

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for the Telegram scraper"""
    
    # Telegram API credentials
    API_ID: Optional[str] = os.getenv('TELEGRAM_API_ID')
    API_HASH: Optional[str] = os.getenv('TELEGRAM_API_HASH')
    
    # Session management
    SESSION_STRING: Optional[str] = os.getenv('TELEGRAM_SESSION_STRING')
    SESSION_FILE: str = 'telegram_session.txt'
    
    # Scraping settings
    MAX_MESSAGES_PER_CHAT: int = int(os.getenv('MAX_MESSAGES_PER_CHAT', '100'))
    SCRAPE_INTERVAL_SECONDS: int = int(os.getenv('SCRAPE_INTERVAL_SECONDS', '60'))
    
    # Rate limiting
    REQUEST_DELAY: float = float(os.getenv('REQUEST_DELAY', '1.0'))
    MAX_REQUESTS_PER_MINUTE: int = int(os.getenv('MAX_REQUESTS_PER_MINUTE', '30'))
    
    # Output settings
    OUTPUT_DIR: str = os.getenv('OUTPUT_DIR', 'scraped_messages')
    OUTPUT_FORMAT: str = os.getenv('OUTPUT_FORMAT', 'json')  # json, csv
    
    # Official account detection
    KNOWN_OFFICIAL_USERNAMES: List[str] = [
        'telegram', 'durov', 'telegramtips', 'bnews', 'previews',
        'botfather', 'botapi', 'telegramdesktop', 'webogram',
        'telegrampassport', 'contest', 'jobs', 'security',
        'translations', 'bugs', 'themes', 'stickers'
    ]
    
    # Logging
    LOG_LEVEL: str = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE: str = os.getenv('LOG_FILE', 'telegram_scraper.log')
    
    @classmethod
    def validate(cls) -> bool:
        """Validate required configuration"""
        if not cls.API_ID or not cls.API_HASH:
            return False
        return True
    
    @classmethod
    def get_session_string(cls) -> Optional[str]:
        """Get session string from environment or file"""
        if cls.SESSION_STRING:
            return cls.SESSION_STRING
        
        try:
            if os.path.exists(cls.SESSION_FILE):
                with open(cls.SESSION_FILE, 'r') as f:
                    return f.read().strip()
        except Exception:
            pass
        
        return None
    
    @classmethod
    def save_session_string(cls, session_string: str) -> bool:
        """Save session string to file"""
        try:
            with open(cls.SESSION_FILE, 'w') as f:
                f.write(session_string)
            return True
        except Exception:
            return False
