# Telegram Recent Message Fetcher

Telegram resmi hesabından son mesajları çeken basit araç.

## 🚀 <PERSON><PERSON><PERSON><PERSON>

```bash
# Bağımlılıkları yükle
pip install -r requirements.txt

# Son 1 saatteki mesajları çek
python telegram_recent_fetcher.py
```

## 📁 Dosyalar

- `telegram_recent_fetcher.py` - <PERSON> script
- `config.py` - <PERSON><PERSON><PERSON>
- `session_manager.py` - Oturum yönetimi  
- `storage.py` - Veri kaydetme
- `account_detector.py` - Hesap tespiti
- `requirements.txt` - Bağımlılıklar
- `telegram_session.txt` - Oturum dosyası
- `scraped_messages/` - Sonuçlar klasörü

## ⚙️ Ayarlar

`.env` dosyasında:
```
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash
```

## 🎯 Özellikler

- ✅ Sadece Telegram resmi he<PERSON> (777000)
- ✅ Son N saatteki mesajlar
- ✅ JSON/CSV çıktı
- ✅ Hızlı ve basit
