"""
Data storage and export functionality
"""
import json
import csv
import os
import asyncio
import aiofiles
from datetime import datetime
from typing import List, Dict, Any
import logging
from config import Config

logger = logging.getLogger(__name__)

class MessageStorage:
    """Handles storage and export of scraped messages"""
    
    def __init__(self):
        self.output_dir = Config.OUTPUT_DIR
        self.ensure_output_directory()
        self.messages: List[Dict[str, Any]] = []
    
    def ensure_output_directory(self):
        """Create output directory if it doesn't exist"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            logger.info(f"Created output directory: {self.output_dir}")
    
    def add_message(self, message_data: Dict[str, Any]):
        """Add a message to the storage"""
        # Add timestamp if not present
        if 'scraped_at' not in message_data:
            message_data['scraped_at'] = datetime.now().isoformat()
        
        self.messages.append(message_data)
        logger.debug(f"Added message from {message_data.get('sender_username', 'unknown')}")
    
    def format_message_data(self, message, sender_info: Dict[str, Any]) -> Dict[str, Any]:
        """Format message data for storage"""
        return {
            'message_id': message.id,
            'date': message.date.isoformat() if message.date else None,
            'text': message.text or '',
            'sender_id': message.sender_id,
            'sender_username': sender_info.get('username'),
            'sender_first_name': sender_info.get('first_name'),
            'sender_last_name': sender_info.get('last_name'),
            'sender_verified': sender_info.get('verified', False),
            'sender_is_bot': sender_info.get('bot', False),
            'sender_is_official': sender_info.get('is_official', False),
            'chat_id': message.chat_id if hasattr(message, 'chat_id') else None,
            'is_reply': message.is_reply if hasattr(message, 'is_reply') else False,
            'has_media': bool(message.media) if hasattr(message, 'media') else False,
            'media_type': self._get_media_type(message),
            'forward_from': self._get_forward_info(message),
            'scraped_at': datetime.now().isoformat()
        }
    
    def _get_media_type(self, message) -> str:
        """Determine the type of media in the message"""
        if not hasattr(message, 'media') or not message.media:
            return 'none'
        
        media_type = type(message.media).__name__
        return media_type.replace('MessageMedia', '').lower()
    
    def _get_forward_info(self, message) -> Dict[str, Any]:
        """Extract forward information from message"""
        if not hasattr(message, 'forward') or not message.forward:
            return {}
        
        forward = message.forward
        return {
            'from_id': getattr(forward, 'from_id', None),
            'from_name': getattr(forward, 'from_name', None),
            'date': forward.date.isoformat() if hasattr(forward, 'date') and forward.date else None,
            'channel_post': getattr(forward, 'channel_post', None)
        }
    
    async def save_to_json(self, filename: str = None) -> str:
        """Save messages to JSON file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"telegram_messages_{timestamp}.json"
        
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            async with aiofiles.open(filepath, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(self.messages, indent=2, ensure_ascii=False))
            
            logger.info(f"Saved {len(self.messages)} messages to {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Failed to save JSON file: {e}")
            raise
    
    async def save_to_csv(self, filename: str = None) -> str:
        """Save messages to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"telegram_messages_{timestamp}.csv"
        
        filepath = os.path.join(self.output_dir, filename)
        
        if not self.messages:
            logger.warning("No messages to save")
            return filepath
        
        try:
            # Get all unique keys from messages
            fieldnames = set()
            for message in self.messages:
                fieldnames.update(message.keys())
            fieldnames = sorted(list(fieldnames))
            
            async with aiofiles.open(filepath, 'w', newline='', encoding='utf-8') as f:
                # Write CSV header
                header = ','.join(fieldnames) + '\n'
                await f.write(header)
                
                # Write data rows
                for message in self.messages:
                    row_data = []
                    for field in fieldnames:
                        value = message.get(field, '')
                        # Handle complex data types
                        if isinstance(value, (dict, list)):
                            value = json.dumps(value)
                        elif value is None:
                            value = ''
                        else:
                            value = str(value)
                        # Escape quotes and commas
                        if ',' in value or '"' in value or '\n' in value:
                            value = '"' + value.replace('"', '""') + '"'
                        row_data.append(value)
                    
                    row = ','.join(row_data) + '\n'
                    await f.write(row)
            
            logger.info(f"Saved {len(self.messages)} messages to {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Failed to save CSV file: {e}")
            raise
    
    async def save(self, format_type: str = None) -> str:
        """Save messages in the specified format"""
        if not format_type:
            format_type = Config.OUTPUT_FORMAT
        
        if format_type.lower() == 'json':
            return await self.save_to_json()
        elif format_type.lower() == 'csv':
            return await self.save_to_csv()
        else:
            raise ValueError(f"Unsupported format: {format_type}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about stored messages"""
        if not self.messages:
            return {'total_messages': 0}
        
        stats = {
            'total_messages': len(self.messages),
            'official_accounts': len([m for m in self.messages if m.get('sender_is_official')]),
            'verified_accounts': len([m for m in self.messages if m.get('sender_verified')]),
            'bot_messages': len([m for m in self.messages if m.get('sender_is_bot')]),
            'messages_with_media': len([m for m in self.messages if m.get('has_media')]),
            'unique_senders': len(set(m.get('sender_id') for m in self.messages if m.get('sender_id')))
        }
        
        # Date range
        dates = [m.get('date') for m in self.messages if m.get('date')]
        if dates:
            stats['date_range'] = {
                'earliest': min(dates),
                'latest': max(dates)
            }
        
        return stats
    
    def clear(self):
        """Clear all stored messages"""
        self.messages.clear()
        logger.info("Cleared all stored messages")
