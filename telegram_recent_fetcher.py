"""
One-time fetcher for recent messages from specific Telegram official account
Fetches only messages from last 1 hour from Telegram (chat_id: 777000)
"""
import asyncio
import logging
from datetime import datetime, timedelta
from telethon import TelegramClient
from telethon.tl.types import User
from session_manager import SessionManager
from storage import MessageStorage
from account_detector import OfficialAccountDetector
from config import Config
from colorama import init, Fore, Style

# Initialize colorama
init()


def print_info(message: str):
    print(f"{Fore.BLUE}ℹ {message}{Style.RESET_ALL}")


def print_success(message: str):
    print(f"{Fore.GREEN}✓ {message}{Style.RESET_ALL}")


def print_error(message: str):
    print(f"{Fore.RED}✗ {message}{Style.RESET_ALL}")


def print_warning(message: str):
    print(f"{Fore.YELLOW}⚠ {message}{Style.RESET_ALL}")


class TelegramRecentFetcher:
    """Fetches recent messages from Telegram official account only"""

    def __init__(self, client: TelegramClient):
        self.client = client
        self.detector = OfficialAccountDetector()
        self.storage = MessageStorage()
        self.target_chat_id = 777000  # Telegram official chat ID
        self.hours_back = 1  # Only last 1 hour

    async def find_telegram_dialog(self):
        """Find the Telegram official dialog (chat_id: 777000)"""
        print_info(
            f"Looking for Telegram official account (chat_id: {self.target_chat_id})...")

        try:
            async for dialog in self.client.iter_dialogs():
                # Check if this is a user dialog
                if not dialog.is_user:
                    continue

                entity = dialog.entity
                if isinstance(entity, User):
                    # Check if this is the Telegram official account
                    if (entity.id == self.target_chat_id or
                            (hasattr(entity, 'first_name') and entity.first_name == 'Telegram')):

                        print_success(
                            f"Found Telegram official account: {entity.first_name} (ID: {entity.id})")
                        return dialog

                # Add small delay
                await asyncio.sleep(0.1)

            print_error(
                f"Telegram official account (chat_id: {self.target_chat_id}) not found in DMs")
            return None

        except Exception as e:
            print_error(f"Error finding Telegram dialog: {e}")
            return None

    async def fetch_recent_messages(self, dialog, hours_back: int = 1):
        """Fetch messages from the last N hours only"""
        entity = dialog.entity
        username = getattr(entity, 'username', 'None')
        first_name = getattr(entity, 'first_name', 'Unknown')

        print_info(
            f"Fetching messages from last {hours_back} hour(s) from {first_name} (@{username})...")

        # Calculate time threshold (use UTC for consistency)
        from datetime import timezone
        now_utc = datetime.now(timezone.utc)
        time_threshold = now_utc - timedelta(hours=hours_back)

        print_info(
            f"Current time (UTC): {now_utc.strftime('%Y-%m-%d %H:%M:%S')}")
        print_info(
            f"Looking for messages newer than: {time_threshold.strftime('%Y-%m-%d %H:%M:%S')} UTC")

        scraped_count = 0
        total_checked = 0

        try:
            # Get sender info once
            sender_info = self.detector.get_account_info(entity)

            print_info("Scanning messages (newest first)...")

            # Iterate through messages (newest first by default)
            # Limit to 50 for debugging
            async for message in self.client.iter_messages(entity, limit=50):
                total_checked += 1

                # Debug: Show message date info
                if message.date:
                    # Convert message date to UTC if it has timezone info
                    msg_date_utc = message.date
                    if msg_date_utc.tzinfo is None:
                        # If no timezone, assume UTC
                        msg_date_utc = msg_date_utc.replace(
                            tzinfo=timezone.utc)

                    print_info(
                        f"Message {total_checked}: {msg_date_utc.strftime('%Y-%m-%d %H:%M:%S')} UTC")

                    # Check if message is older than our threshold
                    if msg_date_utc < time_threshold:
                        print_info(
                            f"Message is older than {hours_back} hour(s). Stopping scan.")
                        break

                    # Process the message (it's within our time range)
                    try:
                        message_data = self.storage.format_message_data(
                            message, sender_info)
                        self.storage.add_message(message_data)
                        scraped_count += 1

                        # Show message details
                        msg_time = msg_date_utc.strftime('%H:%M:%S')
                        msg_preview = (message.text or '')[
                            :50] + '...' if len(message.text or '') > 50 else (message.text or '[No text]')
                        print_success(f"  ✓ {msg_time}: {msg_preview}")

                        # Add small delay
                        await asyncio.sleep(Config.REQUEST_DELAY)

                    except Exception as e:
                        print_warning(
                            f"Error processing message {message.id}: {e}")
                        continue
                else:
                    print_warning(
                        f"Message {total_checked} has no date, skipping")

            print_success(
                f"Fetched {scraped_count} recent messages (checked {total_checked} total)")
            return scraped_count

        except Exception as e:
            print_error(f"Error fetching messages: {e}")
            return 0

    async def run_fetch(self, hours_back: int = 1):
        """Main fetch function"""
        self.hours_back = hours_back

        # Find Telegram dialog
        dialog = await self.find_telegram_dialog()
        if not dialog:
            return 0

        # Fetch recent messages
        count = await self.fetch_recent_messages(dialog, hours_back)

        if count > 0:
            # Show statistics
            stats = self.storage.get_statistics()
            print(f"\n{Fore.CYAN}Fetch Results:{Style.RESET_ALL}")
            print("─" * 20)
            print(f"Messages fetched: {stats.get('total_messages', 0)}")
            print(f"Time range: Last {hours_back} hour(s)")
            print(f"Account: Telegram Official")

            if 'date_range' in stats:
                date_range = stats['date_range']
                print(f"Oldest message: {date_range.get('earliest', 'N/A')}")
                print(f"Newest message: {date_range.get('latest', 'N/A')}")

        return count

    async def save_results(self, format_type: str = 'json'):
        """Save results to file"""
        if not self.storage.messages:
            print_warning("No messages to save")
            return ""

        try:
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"telegram_recent_{timestamp}.{format_type}"

            if format_type == 'json':
                filepath = await self.storage.save_to_json(filename)
            else:
                filepath = await self.storage.save_to_csv(filename)

            print_success(f"Results saved to: {filepath}")
            return filepath
        except Exception as e:
            print_error(f"Failed to save results: {e}")
            raise


async def main():
    """Main function"""
    print(f"{Fore.CYAN}Telegram Recent Message Fetcher{Style.RESET_ALL}")
    print("=" * 40)
    print("Fetches recent messages from Telegram official account only")

    # Get hours back from user
    try:
        hours_input = input(f"\nHours to look back (default: 1): ").strip()
        hours_back = int(hours_input) if hours_input else 1
    except ValueError:
        hours_back = 1

    # Get output format
    format_choice = input(
        "Output format (json/csv, default: json): ").strip().lower()
    output_format = format_choice if format_choice in [
        'json', 'csv'] else 'json'

    print(f"\n{Fore.YELLOW}Configuration:{Style.RESET_ALL}")
    print(f"Target: Telegram official account (chat_id: 777000)")
    print(f"Time range: Last {hours_back} hour(s)")
    print(f"Output format: {output_format}")

    # Validate configuration
    if not Config.validate():
        print_error("Configuration validation failed!")
        return

    if not Config.get_session_string():
        print_error("No session found! Please run setup first.")
        return

    session_manager = SessionManager()

    try:
        # Create and validate client
        print_info("Initializing Telegram client...")
        client = await session_manager.create_client()

        if not await session_manager.validate_session():
            print_error("Session validation failed!")
            return

        print_success("Connected to Telegram")

        # Create fetcher and run
        fetcher = TelegramRecentFetcher(client)
        count = await fetcher.run_fetch(hours_back)

        if count > 0:
            # Save results
            filepath = await fetcher.save_results(output_format)
            print_success(f"Fetch completed! Found {count} recent messages.")
        else:
            print_warning(
                "No recent messages found in the specified time range.")

    except Exception as e:
        print_error(f"Error: {e}")
        logging.exception("Application error")
    finally:
        await session_manager.disconnect()

if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(
        level=logging.WARNING,  # Reduce log noise
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Cancelled by user{Style.RESET_ALL}")
    except Exception as e:
        print_error(f"Application error: {e}")
