# Telegram API Credentials (Required)
# Get these from https://my.telegram.org
TELEGRAM_API_ID=your_api_id_here
TELEGRAM_API_HASH=your_api_hash_here

# Session Management (Optional)
# Leave empty to use file-based session storage
TELEGRAM_SESSION_STRING=

# Scraping Settings (Optional)
MAX_MESSAGES_PER_CHAT=100
SCRAPE_INTERVAL_SECONDS=60

# Rate Limiting (Optional)
REQUEST_DELAY=1.0
MAX_REQUESTS_PER_MINUTE=30

# Output Settings (Optional)
OUTPUT_DIR=scraped_messages
OUTPUT_FORMAT=json

# Logging (Optional)
LOG_LEVEL=INFO
LOG_FILE=telegram_scraper.log
