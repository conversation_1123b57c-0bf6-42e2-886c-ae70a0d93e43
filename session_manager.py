"""
Session management for Telegram client
"""
import asyncio
import logging
from typing import Optional
from telethon import Telegram<PERSON>lient
from telethon.sessions import StringSession
from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError, FloodWaitError
from config import Config

logger = logging.getLogger(__name__)


class SessionManager:
    """Manages Telegram client sessions"""

    def __init__(self):
        self.client: Optional[TelegramClient] = None
        self.session_string: Optional[str] = None

    async def create_client(self) -> TelegramClient:
        """Create and return a Telegram client"""
        if not Config.validate():
            raise ValueError("Missing API_ID or API_HASH in configuration")

        # Try to load existing session
        self.session_string = Config.get_session_string()

        if self.session_string:
            logger.info("Loading existing session...")
            session = StringSession(self.session_string)
        else:
            logger.info("Creating new session...")
            session = StringSession()

        self.client = TelegramClient(
            session,
            Config.API_ID,
            Config.API_HASH
        )

        return self.client

    async def authenticate(self, phone_number: str) -> bool:
        """Authenticate with phone number"""
        if not self.client:
            raise ValueError("Client not initialized")

        try:
            await self.client.connect()

            if await self.client.is_user_authorized():
                logger.info("Already authenticated")
                return True

            logger.info(f"Sending code to {phone_number}")
            await self.client.send_code_request(phone_number)

            # Get code from user
            code = input("Enter the verification code: ")

            try:
                await self.client.sign_in(phone_number, code)
            except SessionPasswordNeededError:
                password = input(
                    "Two-factor authentication enabled. Enter password: ")
                await self.client.sign_in(password=password)

            # Save session string
            self.session_string = self.client.session.save()
            Config.save_session_string(self.session_string)

            logger.info("Authentication successful")
            return True

        except PhoneCodeInvalidError:
            logger.error("Invalid verification code")
            return False
        except FloodWaitError as e:
            wait_time = e.seconds
            hours = wait_time // 3600
            minutes = (wait_time % 3600) // 60
            logger.error(
                f"Flood wait error: Must wait {wait_time} seconds ({hours}h {minutes}m)")
            print(f"\n⚠️  FLOOD WAIT ERROR ⚠️")
            print(
                f"Telegram requires you to wait {hours} hours and {minutes} minutes")
            print(f"before requesting another verification code.")
            print(f"\nThis happens when too many code requests are made.")
            print(f"Please try again after the wait period expires.")
            return False
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return False

    async def validate_session(self) -> bool:
        """Validate current session"""
        if not self.client:
            return False

        try:
            await self.client.connect()
            if await self.client.is_user_authorized():
                me = await self.client.get_me()
                logger.info(
                    f"Session valid for user: {me.first_name} (@{me.username})")
                return True
            else:
                logger.warning("Session expired or invalid")
                return False
        except Exception as e:
            logger.error(f"Session validation failed: {e}")
            return False

    async def disconnect(self):
        """Disconnect the client"""
        if self.client:
            await self.client.disconnect()
            logger.info("Client disconnected")

    def get_client(self) -> Optional[TelegramClient]:
        """Get the current client instance"""
        return self.client
