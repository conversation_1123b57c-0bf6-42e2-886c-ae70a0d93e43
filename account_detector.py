"""
Official Telegram account detection
"""
import logging
from typing import List, Optional
from telethon.tl.types import <PERSON><PERSON>, <PERSON><PERSON>, Channel
from config import Config

logger = logging.getLogger(__name__)

class OfficialAccountDetector:
    """Detects official Telegram accounts"""
    
    def __init__(self):
        self.official_usernames = set(Config.KNOWN_OFFICIAL_USERNAMES)
        self.verified_cache = {}
    
    def is_official_account(self, entity) -> bool:
        """
        Determine if an account is official based on multiple criteria
        """
        if not entity:
            return False
        
        # Check if it's a user
        if isinstance(entity, User):
            return self._is_official_user(entity)
        
        # Check if it's a channel or chat
        elif isinstance(entity, (Chat, Channel)):
            return self._is_official_channel(entity)
        
        return False
    
    def _is_official_user(self, user: User) -> bool:
        """Check if a user is official"""
        # Check if user is verified (blue checkmark)
        if hasattr(user, 'verified') and user.verified:
            logger.debug(f"User {user.username} is verified")
            return True
        
        # Check if username is in known official list
        if user.username and user.username.lower() in self.official_usernames:
            logger.debug(f"User {user.username} is in official username list")
            return True
        
        # Check for Telegram team indicators
        if self._is_telegram_team_member(user):
            logger.debug(f"User {user.username} appears to be Telegram team member")
            return True
        
        # Check for official bot patterns
        if user.bot and self._is_official_bot(user):
            logger.debug(f"Bot {user.username} appears to be official")
            return True
        
        return False
    
    def _is_official_channel(self, channel) -> bool:
        """Check if a channel is official"""
        # Check if channel is verified
        if hasattr(channel, 'verified') and channel.verified:
            return True
        
        # Check if username is in known official list
        if hasattr(channel, 'username') and channel.username:
            if channel.username.lower() in self.official_usernames:
                return True
        
        # Check for official channel patterns
        if hasattr(channel, 'title') and channel.title:
            title_lower = channel.title.lower()
            if any(keyword in title_lower for keyword in ['telegram', 'official', 'news']):
                # Additional verification needed for title-based detection
                return hasattr(channel, 'verified') and channel.verified
        
        return False
    
    def _is_telegram_team_member(self, user: User) -> bool:
        """Check if user appears to be a Telegram team member"""
        if not user.username:
            return False
        
        username_lower = user.username.lower()
        
        # Known Telegram team member patterns
        team_patterns = [
            'durov', 'telegram', 'pavel', 'nikolai'
        ]
        
        return any(pattern in username_lower for pattern in team_patterns)
    
    def _is_official_bot(self, user: User) -> bool:
        """Check if bot appears to be official"""
        if not user.bot or not user.username:
            return False
        
        username_lower = user.username.lower()
        
        # Known official bot patterns
        official_bot_patterns = [
            'botfather', 'botapi', 'gamebot', 'gif', 'pic',
            'bold', 'vote', 'like', 'bing', 'wiki', 'imdb',
            'youtube', 'music', 'vid', 'telegraph', 'jobs'
        ]
        
        # Check if bot username matches official patterns
        for pattern in official_bot_patterns:
            if pattern in username_lower:
                return True
        
        # Check if bot ends with official suffixes
        official_suffixes = ['bot', '_bot']
        if any(username_lower.endswith(suffix) for suffix in official_suffixes):
            # Additional check for official-looking names
            base_name = username_lower.replace('bot', '').replace('_', '')
            if base_name in self.official_usernames:
                return True
        
        return False
    
    def get_account_info(self, entity) -> dict:
        """Get detailed information about an account"""
        info = {
            'is_official': self.is_official_account(entity),
            'type': type(entity).__name__,
            'id': entity.id if hasattr(entity, 'id') else None,
            'username': getattr(entity, 'username', None),
            'verified': getattr(entity, 'verified', False),
            'bot': getattr(entity, 'bot', False),
            'title': getattr(entity, 'title', None),
            'first_name': getattr(entity, 'first_name', None),
            'last_name': getattr(entity, 'last_name', None)
        }
        
        return info
    
    def add_official_username(self, username: str):
        """Add a username to the official list"""
        self.official_usernames.add(username.lower())
        logger.info(f"Added {username} to official usernames list")
    
    def remove_official_username(self, username: str):
        """Remove a username from the official list"""
        self.official_usernames.discard(username.lower())
        logger.info(f"Removed {username} from official usernames list")
